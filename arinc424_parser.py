#!/usr/bin/env python3
"""
ARINC424 Parser and Map Visualization Tool

This tool parses ARINC424 navigation data files and creates interactive maps
showing airports, waypoints, SIDs, STARs, and their relationships.

Features:
- Filter by region and ICAO codes
- Extract and visualize SID/STAR procedures
- Plot waypoint sequences with connecting lines
- Interactive map with filtering controls
"""

import sys
import os
import re
import argparse
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from collections import defaultdict
import json

# Add the arinc424 library to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import arinc424
from arinc424.record import Record


@dataclass
class Coordinate:
    """Represents a geographic coordinate"""
    latitude: float
    longitude: float
    
    @classmethod
    def from_arinc424(cls, lat_str: str, lon_str: str) -> 'Coordinate':
        """Parse ARINC424 coordinate format (e.g., N34113883, W083582481)"""
        # Latitude: N/S + degrees (2) + minutes (2) + seconds (4)
        lat_sign = 1 if lat_str[0] == 'N' else -1
        lat_deg = int(lat_str[1:3])
        lat_min = int(lat_str[3:5])
        lat_sec = int(lat_str[5:9]) / 100.0  # Last 4 digits are seconds * 100
        latitude = lat_sign * (lat_deg + lat_min/60.0 + lat_sec/3600.0)
        
        # Longitude: E/W + degrees (3) + minutes (2) + seconds (4)
        lon_sign = 1 if lon_str[0] == 'E' else -1
        lon_deg = int(lon_str[1:4])
        lon_min = int(lon_str[4:6])
        lon_sec = int(lon_str[6:10]) / 100.0  # Last 4 digits are seconds * 100
        longitude = lon_sign * (lon_deg + lon_min/60.0 + lon_sec/3600.0)
        
        return cls(latitude, longitude)


@dataclass
class Airport:
    """Represents an airport"""
    icao: str
    name: str
    coordinate: Coordinate
    region: str
    
    
@dataclass
class Waypoint:
    """Represents a waypoint"""
    identifier: str
    coordinate: Coordinate
    waypoint_type: str
    region: str
    icao_code: str
    

@dataclass
class ProcedureStep:
    """Represents a step in a SID/STAR procedure"""
    sequence: int
    waypoint_id: str
    waypoint_icao: str
    waypoint_section: str
    path_type: str
    altitude_desc: str = ""
    altitude: str = ""
    

@dataclass
class Procedure:
    """Represents a SID or STAR procedure"""
    airport_icao: str
    procedure_id: str
    procedure_type: str  # 'SID' or 'STAR'
    transition: str
    steps: List[ProcedureStep]


class ARINC424Parser:
    """Main parser for ARINC424 data"""
    
    def __init__(self):
        self.airports: Dict[str, Airport] = {}
        self.waypoints: Dict[str, Waypoint] = {}
        self.procedures: List[Procedure] = []
        self.region_airports: Dict[str, Set[str]] = defaultdict(set)
        
    def parse_file(self, filepath: str) -> None:
        """Parse an ARINC424 file and extract all relevant data"""
        print(f"Parsing ARINC424 file: {filepath}")
        
        with open(filepath, 'r') as f:
            for line_num, line in enumerate(f, 1):
                if line_num % 10000 == 0:
                    print(f"Processed {line_num} lines...")
                    
                try:
                    record = Record()
                    if record.read(line):
                        self._process_record(record)
                except Exception as e:
                    # Skip invalid records
                    continue
                    
        print(f"Parsing complete. Found:")
        print(f"  - {len(self.airports)} airports")
        print(f"  - {len(self.waypoints)} waypoints") 
        print(f"  - {len(self.procedures)} procedures")
        
    def _process_record(self, record: Record) -> None:
        """Process a single ARINC424 record"""
        section_code = record.ident
        
        if section_code == 'PA':  # Airport records
            self._process_airport_record(record)
        elif section_code in ['EA', 'PC']:  # Waypoint records
            self._process_waypoint_record(record)
        elif section_code in ['PD', 'PE', 'PF']:  # SID/STAR/Approach records
            self._process_procedure_record(record)
            
    def _process_airport_record(self, record: Record) -> None:
        """Process airport record"""
        try:
            raw = record.raw
            icao = raw[6:10].strip()
            name = raw[93:123].strip()
            lat_str = raw[32:41]
            lon_str = raw[42:51]  # Fixed: was 41:51, should be 42:51
            region = raw[1:4]

            if lat_str.strip() and lon_str.strip() and icao:
                coordinate = Coordinate.from_arinc424(lat_str, lon_str)
                airport = Airport(icao, name, coordinate, region)
                self.airports[icao] = airport
                self.region_airports[region].add(icao)
        except Exception as e:
            pass  # Skip malformed records
            
    def _process_waypoint_record(self, record: Record) -> None:
        """Process waypoint record"""
        try:
            raw = record.raw
            identifier = raw[13:18].strip()
            lat_str = raw[32:41]  # Fixed: coordinates start at position 32
            lon_str = raw[41:51]  # Fixed: longitude starts at position 41
            waypoint_type = raw[26:29].strip()
            region = raw[6:10].strip()
            icao_code = raw[10:12].strip()

            if lat_str.strip() and lon_str.strip() and identifier:
                coordinate = Coordinate.from_arinc424(lat_str, lon_str)
                waypoint = Waypoint(identifier, coordinate, waypoint_type, region, icao_code)
                # Use a composite key to handle duplicate waypoint names in different regions
                key = f"{identifier}_{icao_code}_{region}"
                self.waypoints[key] = waypoint
        except Exception as e:
            pass  # Skip malformed records
            
    def _process_procedure_record(self, record: Record) -> None:
        """Process SID/STAR/Approach record"""
        try:
            raw = record.raw
            
            # Only process primary records (continuation = '0' or '1')
            if raw[38] not in ['0', '1']:
                return
                
            airport_icao = raw[6:10].strip()
            procedure_id = raw[13:19].strip()
            route_type = raw[19]
            transition = raw[20:25].strip()
            sequence = raw[26:29].strip()
            fix_identifier = raw[29:34].strip()
            fix_icao = raw[34:36].strip()
            fix_section = raw[36:38].strip()
            
            # Determine procedure type
            section_code = record.ident
            if section_code == 'PD':
                proc_type = 'SID'
            elif section_code == 'PE':
                proc_type = 'STAR'
            else:
                proc_type = 'APPROACH'
                
            # Extract waypoint description and path type
            waypoint_desc = raw[39:43].strip()
            path_type = waypoint_desc[:2] if len(waypoint_desc) >= 2 else ""
            
            # Extract altitude information
            altitude_desc = raw[82:83]
            altitude = raw[84:89].strip()
            
            if sequence and fix_identifier:
                step = ProcedureStep(
                    sequence=int(sequence),
                    waypoint_id=fix_identifier,
                    waypoint_icao=fix_icao,
                    waypoint_section=fix_section,
                    path_type=path_type,
                    altitude_desc=altitude_desc,
                    altitude=altitude
                )
                
                # Find or create procedure
                proc_key = f"{airport_icao}_{procedure_id}_{transition}_{proc_type}"
                existing_proc = None
                for proc in self.procedures:
                    if (proc.airport_icao == airport_icao and 
                        proc.procedure_id == procedure_id and
                        proc.transition == transition and
                        proc.procedure_type == proc_type):
                        existing_proc = proc
                        break
                        
                if existing_proc:
                    existing_proc.steps.append(step)
                else:
                    new_proc = Procedure(
                        airport_icao=airport_icao,
                        procedure_id=procedure_id,
                        procedure_type=proc_type,
                        transition=transition,
                        steps=[step]
                    )
                    self.procedures.append(new_proc)
                    
        except Exception:
            pass  # Skip malformed records

    def filter_by_region(self, region: str) -> List[str]:
        """Get all airport ICAOs in a specific region"""
        return list(self.region_airports.get(region, set()))

    def filter_by_icao(self, icao_codes: List[str]) -> List[str]:
        """Filter airports by ICAO codes"""
        return [icao for icao in icao_codes if icao in self.airports]

    def get_procedures_for_airports(self, airport_icaos: List[str],
                                  procedure_types: List[str] = None) -> List[Procedure]:
        """Get all procedures for specified airports"""
        if procedure_types is None:
            procedure_types = ['SID', 'STAR']

        filtered_procedures = []
        for proc in self.procedures:
            if (proc.airport_icao in airport_icaos and
                proc.procedure_type in procedure_types):
                filtered_procedures.append(proc)

        return filtered_procedures

    def resolve_waypoint_coordinates(self, procedure: Procedure) -> List[Tuple[str, Optional[Coordinate]]]:
        """Resolve coordinates for all waypoints in a procedure"""
        waypoint_coords = []

        for step in sorted(procedure.steps, key=lambda x: x.sequence):
            waypoint_id = step.waypoint_id
            waypoint_icao = step.waypoint_icao
            waypoint_section = step.waypoint_section

            # Try different key combinations to find the waypoint
            possible_keys = [
                f"{waypoint_id}_{waypoint_icao}_{waypoint_section}",
                f"{waypoint_id}_{waypoint_icao}_",
                f"{waypoint_id}__{waypoint_section}",
                f"{waypoint_id}__",
            ]

            coordinate = None
            for key in possible_keys:
                if key in self.waypoints:
                    coordinate = self.waypoints[key].coordinate
                    break

            # Also try searching by just the waypoint identifier
            if coordinate is None:
                for wp_key, waypoint in self.waypoints.items():
                    if waypoint.identifier == waypoint_id:
                        coordinate = waypoint.coordinate
                        break

            waypoint_coords.append((waypoint_id, coordinate))

        return waypoint_coords

    def get_available_regions(self) -> List[str]:
        """Get list of all available regions"""
        return sorted(list(self.region_airports.keys()))

    def get_airports_in_region(self, region: str) -> List[Airport]:
        """Get all airports in a specific region"""
        airport_icaos = self.region_airports.get(region, set())
        return [self.airports[icao] for icao in airport_icaos if icao in self.airports]
